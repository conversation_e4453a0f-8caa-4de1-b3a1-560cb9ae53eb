<g:render template="/${session['entryController']}/navheader_new"></g:render>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>

<link rel="stylesheet" href="/assets/chat/chat.css">
<link rel="stylesheet" href="/assets/chat/chatInput.css">

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<style>
    /* Global Styles */
    * {
        box-sizing: border-box;
    }

    html{
        overflow: hidden;
    }
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
        line-height: 1.6;
    }
    .footer-menus,
    .mobile-footer-nav{
        display: none !important;
    }
    .menu-main-min-height{
        min-height: 75px !important;
    }
    @media (max-width: 768px){
        .panels-container .left-panel .panel-content{
            padding: 0;
        }
        .ebook-container{
            padding: 0 !important;
        }
        .ebook-layout{
            padding: 0 !important;
        }
        .content-area{
            padding: 0 !important;
        }
    }
</style>

<!-- Main Chat Container -->
<div id="chat-app" class="chat-app">
    <!-- Top Header with Toggle -->
    <div class="app-header">
        <div class="header-content">
            <button class="back-btn" style="display: none;">
                <span>Back</span>
            </button>
            <g:if test="${previewMode}">
                <div class="header-buy-now-section">
                    <button class="gptBuyNowBtn header-buy-btn" onclick="addToCart('${booksMst.id}', '${bookPriceDtl.bookType}')">Buy Now</button>
                </div>
            </g:if>
            <div class="toggle-wrapper">
                <small class="toggle-label toggle-label-color">Read Mode</small>
                <button id="toggle-btn" class="toggle-btn active" title="Toggle Full Width"></button>
            </div>
        </div>
    </div>

    <!-- Panels Container -->
    <div id="panels-container" class="panels-container">
        <!-- Left Panel -->
        <div id="left-panel" class="chat-panel left-panel">
            <div class="panel-content">
                <!-- Left Panel Content -->
                <g:render template="/wpmain/aiContent"></g:render>
            </div>
        </div>

        <!-- Resizable Divider -->
        <div id="divider" class="divider">
            <div class="divider-handle">
                <i class="fas fa-grip-lines-vertical"></i>
            </div>
        </div>

        <!-- Right Panel (Chat Content) -->
        <div id="right-panel" class="chat-panel right-panel">
            <div class="panel-content">
                <g:render template="/chat/rightContent"></g:render>
            </div>
        </div>
    </div>
</div>

<!-- Include cart scripts for Buy Now functionality -->
<g:render template="/wsshop/cartScripts"></g:render>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>

<script src="/assets/chat/ChatApp.js"></script>
<script src="/assets/chat/ResizableDivider.js"></script>
<script>

    document.addEventListener('DOMContentLoaded', function() {

        const chatApp = new ChatApp();
        window.chatApp = chatApp;
        const chatMessages = document.getElementById('chat-messages');

        // Initialize the ResizableDivider
        const resizableDivider = new ResizableDivider({
            dividerId: 'divider',
            leftPanelId: 'left-panel',
            rightPanelId: 'right-panel',
            containerId: 'panels-container',
            minPanelSize: 50,
            enabled: true
        });

        // Add event listener for toggle button
        document.getElementById('toggle-btn').addEventListener('click', () => {
            chatApp.toggle('toggle-btn')();
            // Update divider state after toggle
            setTimeout(() => {
                resizableDivider.updateState();
            }, 50);
        });

        document.querySelector('.back-btn').addEventListener('click', () => {
            chatApp.openToggle('toggle-btn')();
        });
    });
</script>

