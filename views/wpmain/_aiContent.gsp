<link rel="stylesheet" href="/assets/wpmain/aiContent.css">

<script>
    var loggedIn = false;
    var currentChapterId = null;
    var chapterData = {};
    var namespace = null;
    var resId ="${resId}";
    var username = ""
    <%if(session["userdetails"]!=null){%>
        username = '${session["userdetails"].username}'
        username = username.replace("&#64;",'@')
    <%}%>
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>

<script>
    // Pass chapters data to JavaScript
    var chaptersData = [
        <g:each in="${chaptersList}" var="chapter" status="index">
        {
            id: ${chapter.chapterId},
            name: "${chapter.name}",
            previewChapter: "${chapter.previewChapter}"
        }${index < chaptersList.size() - 1 ? ',' : ''}
        </g:each>
    ];
</script>
<div class="ebook-container">
    <!-- Header Section -->
    <div class="ebook-header">
        <h1 class="book-title">${booksMst.title}</h1>
    </div>

    <!-- Main Layout -->
    <div class="ebook-layout">
        <!-- Chapter Selection -->
        <div class="chapter-selection">
            <h3>Select Chapter</h3>
            <div class="chapter-selection-content">
                <div class="chapter-dropdown-section">
                    <div class="chapter-dropdown-wrapper">
                        <select class="chapter-dropdown" id="chapterDropdown" onchange="loadChapterFromDropdown(this.value)">
                            <option value="">-- Select a chapter --</option>
                            <g:each in="${chaptersList}" var="chapter" status="index">
                                <g:if test="${previewMode}">
                                    <g:if test="${chapter.previewChapter == 'true'}">
                                        <option value="${chapter.chapterId}" ${index == 0 ? 'selected' : ''}>
                                            ${chapter.name}
                                        </option>
                                    </g:if>
                                    <g:else>
                                        <option value="" disabled class="chapter-option-locked">
                                            🔒 ${chapter.name}
                                        </option>
                                    </g:else>
                                </g:if>
                                <g:else>
                                    <option value="${chapter.chapterId}" ${index == 0 ? 'selected' : ''}>
                                        ${chapter.name}
                                    </option>
                                </g:else>
                            </g:each>
                        </select>
                    </div>
                </div>
                <g:if test="${previewMode}">
                    <div class="buy-now-section">
                        <button class="gptBuyNowBtn" id="gptBuyNowBtn" onclick="addToCart('${booksMst.id}', '${bookPriceDtl.bookType}')">Buy Now</button>
                    </div>
                </g:if>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner"></div>
                <p>Loading chapter content...</p>
            </div>

            <!-- Chapter Summary -->
            <div class="chapter-summary" id="chapterSummary" style="display: none;">
                <h2 class="summary-title">
                    <i class="fas fa-chart-bar"></i> Chapter Overview
                </h2>
                <div class="question-type-nav" id="questionTypeNav">
                    <!-- Question type navigation will be populated by JavaScript -->
                </div>
            </div>

            <!-- Exercise Solutions Section -->
            <div class="content-section" id="exerciseSolutionsSection" style="display: none;">
                <div class="section-header">
                    <h2><i class="fas fa-pencil-alt"></i> Exercise Solutions</h2>
                    <p class="section-description">Practice problems with detailed solutions and explanations</p>
                </div>
                <div class="questions-container" id="exerciseSolutionsContainer">
                    <!-- Exercise solutions will be populated by JavaScript -->
                </div>
            </div>

            <!-- Question Bank Section -->
            <div class="content-section" id="questionBankSection" style="display: none;">
                <div class="section-header">
                    <h2><i class="fas fa-database"></i> Question Bank</h2>
                    <p class="section-description">Comprehensive collection of practice questions by type</p>
                </div>
                <div class="question-types-container" id="questionTypesContainer">
                    <!-- Question types will be populated by JavaScript -->
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="welcome-message" id="welcomeMessage">
                <div style="text-align: center; padding: 60px 20px;">
                    <i class="fas fa-book-reader" style="font-size: 4rem; color: #667eea; margin-bottom: 20px;"></i>
                    <h2 style="color: #333; margin-bottom: 15px;">Welcome to Your AI-Enabled Ebook</h2>
                    <p style="color: #666; font-size: 1.1rem; max-width: 600px; margin: 0 auto;">
                        Select a chapter from the navigation to start exploring interactive content,
                        practice questions, and detailed explanations powered by AI.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="feedback-modal" id="feedbackModal">
    <div class="feedback-modal-content">
        <div class="feedback-modal-header">
            <h3 class="feedback-modal-title">
                <i class="fas fa-flag"></i> Report Issue / Give Feedback
            </h3>
            <button class="feedback-modal-close" onclick="closeFeedbackModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="feedback-modal-body">
            <div class="feedback-options">
                <div class="feedback-option">
                    <input type="radio" id="wrongAnswer" name="issueType" value="Wrong answer">
                    <label for="wrongAnswer">Wrong answer</label>
                </div>
                <div class="feedback-option">
                    <input type="radio" id="spellingMistakes" name="issueType" value="Spelling mistakes">
                    <label for="spellingMistakes">Spelling mistakes</label>
                </div>
                <div class="feedback-option">
                    <input type="radio" id="explanationNotClear" name="issueType" value="Explanation not clear">
                    <label for="explanationNotClear">Explanation not clear</label>
                </div>
            </div>
            <textarea class="feedback-textarea" id="feedbackDetails"
                      placeholder="Please provide more details about the issue or enter any other feedback..."></textarea>
        </div>
        <div class="feedback-modal-footer">
            <button class="feedback-btn feedback-btn-cancel" onclick="closeFeedbackModal()">
                Cancel
            </button>
            <button class="feedback-btn feedback-btn-submit" onclick="submitFeedback()">
                Submit Feedback
            </button>
        </div>
    </div>
</div>

<!-- Go to Top Button -->
<button class="go-to-top" id="goToTopBtn" onclick="scrollToTop()">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- Toast Notification -->
<div class="toast" id="toast"></div>

<!-- JavaScript -->
<script>
    // Global variables
    var currentChapterId = null;
    var chapterData = {};
    var previewMode = ${params.previewMode == 'true' ? 'true' : 'false'};
    var previewChapterId = null;
    var siteName="";
    var isStorePdfVectorsInProgress = false;
    var pendingRetrieveDataRequest = null;
    var activeRetrieveDataControllers = new Set(); // Track active AbortControllers
    var isInitialPageLoad = true; // Track if this is the first chapter load
    <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
    siteName="${session['siteName']}";
    <%}else{%>
    siteName="${session['entryController']}";
    <% }%>

    // Question type mappings
    var questionTypeMap = {
        "LongAnswer": { title: "Long Answer Type", icon: "fas fa-align-left" },
        "ShortAnswer": { title: "Short Answer Type", icon: "fas fa-align-center" },
        "VeryShortAnswer": { title: "Very Short Answer Type", icon: "fas fa-align-justify" },
        "AssertionReason": { title: "Assertion / Reasoning Type", icon: "fas fa-balance-scale" },
        "Problem": { title: "Problem", icon: "fas fa-calculator" },
        "Multiple Choice Questions": { title: "Multiple Choice Questions", icon: "fas fa-list-ul" },
        "FillBlank": { title: "Fill in the Blanks", icon: "fas fa-edit" },
        "TrueFalse": { title: "True or False", icon: "fas fa-check-circle" },
        "MatchFollowing": { title: "Match the Following", icon: "fas fa-link" },
        "ArrangeSequence": { title: "Arrange in Right Sequence", icon: "fas fa-sort-numeric-down" }
    };

    // Initialize on page load
    document.addEventListener("DOMContentLoaded", function() {
        // Initialize copy protection
        initializeCopyProtection();

        // Initialize preview mode
        if (previewMode) {
            initializePreviewMode();
        }

        // Load first available chapter by default
        var dropdown = document.getElementById("chapterDropdown");
        if (dropdown) {
            var firstAvailableOption = dropdown.querySelector("option:not([disabled]):not([value=''])");
            if (firstAvailableOption) {
                dropdown.value = firstAvailableOption.value;
                loadChapterFromDropdown(firstAvailableOption.value);
            }
        }

        // Initialize scroll listener for go-to-top button
        var panelContent = document.querySelector('.panel-content');
        if (panelContent) {
            panelContent.addEventListener("scroll", handleScroll);
        } else {
            // Fallback to window scroll if panel not found
            window.addEventListener("scroll", handleScroll);
        }
    })

    // Initialize copy protection
    function initializeCopyProtection() {
        // Disable keyboard shortcuts for copy (Ctrl+C, Cmd+C)
        document.addEventListener('keydown', function(e) {
            // Check for Ctrl+C (Windows/Linux) or Cmd+C (Mac)
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 67) {
                e.preventDefault();
                e.stopPropagation();
                showToast("Content copying is not allowed");
                return false;
            }

            // Also disable Ctrl+A (Select All), Ctrl+X (Cut), Ctrl+V (Paste in dev tools)
            if ((e.ctrlKey || e.metaKey) && (e.keyCode === 65 || e.keyCode === 88)) {
                e.preventDefault();
                e.stopPropagation();
                showToast("Content selection is not allowed");
                return false;
            }

            // Disable F12, Ctrl+Shift+I, Ctrl+U (Developer tools)
            if (e.keyCode === 123 ||
                ((e.ctrlKey || e.metaKey) && e.shiftKey && e.keyCode === 73) ||
                ((e.ctrlKey || e.metaKey) && e.keyCode === 85)) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        });

        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            showToast("Right-click is disabled");
            return false;
        });

        // Disable print
        window.addEventListener('beforeprint', function(e) {
            e.preventDefault();
            showToast("Printing is not allowed");
            return false;
        });

        // Disable copy via execCommand
        document.addEventListener('copy', function(e) {
            e.clipboardData.setData('text/plain', '');
            e.preventDefault();
            showToast("Content copying is not allowed");
        });

        // Override document.execCommand for copy operations
        var originalExecCommand = document.execCommand;
        document.execCommand = function(command) {
            if (command === 'copy' || command === 'cut' || command === 'selectAll') {
                showToast("Content copying is not allowed");
                return false;
            }
            return originalExecCommand.apply(document, arguments);
        };


    }

    // Initialize preview mode
    function initializePreviewMode() {
        // Find the preview chapter from chapters data
        for (var i = 0; i < chaptersData.length; i++) {
            var chapter = chaptersData[i];
            if (chapter.previewChapter === "true") {
                previewChapterId = chapter.id.toString();
                break;
            }
        }

        console.log("Preview mode enabled. Preview chapter ID:", previewChapterId);

        // If no preview chapter found, use the first available chapter
        if (!previewChapterId && chaptersData.length > 0) {
            previewChapterId = chaptersData[0].id.toString();
            console.log("No preview chapter found, using first chapter:", previewChapterId);
        }
    }

    // Handle dropdown chapter selection
    function loadChapterFromDropdown(chapterId) {
        if (chapterId) {
            // In preview mode, only allow loading the preview chapter
            if (previewMode && chapterId !== previewChapterId) {
                showToast("This chapter is locked. Please unlock the book to access all chapters.");

                // Reset dropdown to preview chapter
                var dropdown = document.getElementById("chapterDropdown");
                if (dropdown) {
                    dropdown.value = previewChapterId;
                }
                return;
            }

            // Only abort pending requests if this is not the initial page load
            if (!isInitialPageLoad) {
                // Abort any pending retrieveData requests when chapter changes
                abortAllPendingRequests();

                // Reset chat messages when chapter changes
                if (window.chatApp && typeof window.chatApp.resetChatMessages === 'function') {
                    window.chatApp.resetChatMessages();
                }
            } else {
                // Mark that initial load is complete
                isInitialPageLoad = false;
            }

            loadChapter(chapterId);
        } else {
            hideAllSections();
            document.getElementById("welcomeMessage").style.display = "block";
        }
    }

    // Abort all pending retrieveData requests
    function abortAllPendingRequests() {
        console.log("Aborting all pending retrieveData requests due to chapter change");

        // Abort all active fetch requests
        activeRetrieveDataControllers.forEach(controller => {
            try {
                controller.abort();
            } catch (error) {
                console.log("Error aborting request:", error);
            }
        });

        // Clear the set of active controllers
        activeRetrieveDataControllers.clear();

        // Clear any pending request
        pendingRetrieveDataRequest = null;

        // Reset chat app state and abort any active requests
        if (window.chatApp) {
            window.chatApp.abortActiveRequest();
        }

        showToast("Previous requests cancelled due to chapter change");
    }

    // Load chapter content
    function loadChapter(chapterId) {
        // Update dropdown selection
        var dropdown = document.getElementById("chapterDropdown");
        if (dropdown) {
            dropdown.value = chapterId;
        }

        // Show loading spinner
        showLoading();

        // Hide all content sections
        hideAllSections();

        currentChapterId = chapterId;

        // Fetch chapter overview data (includes exercise solutions as first section)
        fetch("/wpmain/getChapterContent?chapterId=" + chapterId + "&loadType=overview")
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                chapterData[chapterId] = data;
                namespace = data.namespace;
                resId = data.resId;
                displayChapterOverview(data);
                hideLoading();
                checkAndUpdateNS(namespace);
            })
            .catch(function(error) {
                console.error("Error loading chapter:", error);
                showToast("Error loading chapter content");
                hideLoading();
            });
    }

    // Display chapter overview (initial load)
    function displayChapterOverview(data) {
        // Store the question type counts for later use
        window.chapterQuestionTypeCounts = data.questionTypeCounts || {};

        displayChapterSummary(data.questionTypeCounts);
        displayExerciseSolutions(data.exerciseSolutions);

        // Initialize question bank section with placeholders
        initializeQuestionBankSection();

        // Show content sections
        document.getElementById("chapterSummary").style.display = "block";
        // Exercise Solutions section visibility is handled by displayExerciseSolutions function
        document.getElementById("questionBankSection").style.display = "block";

        // Render math formulas for loaded content
        renderMathFormulas();

        // Load chat history for the current chapter
        if (window.chatApp && typeof window.chatApp.loadChatHistory === 'function' && data.resId) {
            window.chatApp.loadChatHistory(data.resId);
        }
    }

    // Initialize question bank section with loading placeholders
    function initializeQuestionBankSection() {
        var container = document.getElementById("questionTypesContainer");
        var html = "";

        // Define the order of question types as specified
        var questionTypeOrder = [
            "LongAnswer", "ShortAnswer", "VeryShortAnswer", "AssertionReason", "Problem",
            "Multiple Choice Questions", "FillBlank", "TrueFalse", "MatchFollowing", "ArrangeSequence"
        ];

        var questionTypeMap = {
            "LongAnswer": { title: "Long Answer Questions", icon: "fas fa-align-left" },
            "ShortAnswer": { title: "Short Answer Questions", icon: "fas fa-align-center" },
            "VeryShortAnswer": { title: "Very Short Answer Questions", icon: "fas fa-align-justify" },
            "AssertionReason": { title: "Assertion & Reason", icon: "fas fa-balance-scale" },
            "Problem": { title: "Problem Solving", icon: "fas fa-calculator" },
            "Multiple Choice Questions": { title: "Multiple Choice Questions", icon: "fas fa-list-ul" },
            "FillBlank": { title: "Fill in the Blanks", icon: "fas fa-edit" },
            "TrueFalse": { title: "True/False", icon: "fas fa-check-circle" },
            "MatchFollowing": { title: "Match the Following", icon: "fas fa-link" },
            "ArrangeSequence": { title: "Arrange the Sequence", icon: "fas fa-sort-numeric-down" }
        };

        questionTypeOrder.forEach(function(qType) {
            if (window.chapterQuestionTypeCounts[qType] && window.chapterQuestionTypeCounts[qType] > 0) {
                var typeInfo = questionTypeMap[qType];
                if (typeInfo) {
                    html += createQuestionTypePlaceholder(qType, typeInfo, window.chapterQuestionTypeCounts[qType]);
                }
            }
        });

        if (html === "") {
            html = "<div class=\"no-data-message\">" +
                "<div class=\"no-data-icon\"><i class=\"fas fa-info-circle\"></i></div>" +
                "<p>No question bank content available for this chapter.</p>" +
                "</div>";
        }

        container.innerHTML = html;
    }

    // Create question type placeholder with loading state
    function createQuestionTypePlaceholder(qType, typeInfo, count) {
        var sectionId = qType === "Multiple Choice Questions" ? "section-mcq" : "section-" + qType.replace(/\s+/g, "-");
        var html = "<div class=\"question-type-section\" id=\"" + sectionId + "\">" +
            "<div class=\"question-type-header\" onclick=\"loadQuestionTypeData('" + qType + "', '" + sectionId + "')\">" +
            "<div>" +
            "<i class=\"" + typeInfo.icon + "\"></i> " +
            "<span class=\"question-type-title\">" + typeInfo.title + "</span>" +
            "</div>" +
            "<div>" +
            "<span class=\"question-type-count\">" + count + " questions</span>" +
            "<i class=\"fas fa-chevron-down toggle-icon\" id=\"toggle-" + sectionId + "\"></i>" +
            "</div>" +
            "</div>" +
            "<div class=\"question-type-content\" id=\"content-" + sectionId + "\">" +
            "<div class=\"section-loader\" id=\"loader-" + sectionId + "\" style=\"display: none;\">" +
            "<div class=\"spinner\"></div>" +
            "<p>Loading questions...</p>" +
            "</div>" +
            "<div class=\"section-content\" id=\"questions-" + sectionId + "\"></div>" +
            "</div>" +
            "</div>";
        return html;
    }

    // Load question type data lazily
    function loadQuestionTypeData(qType, sectionId) {
        var content = document.getElementById("content-" + sectionId);
        var toggle = document.getElementById("toggle-" + sectionId);
        var loader = document.getElementById("loader-" + sectionId);
        var questionsContainer = document.getElementById("questions-" + sectionId);

        // If already loaded, just toggle
        if (questionsContainer.innerHTML.trim() !== "") {
            toggleQuestionTypeContent(content, toggle);
            return;
        }

        // Show loading state
        content.classList.add("open");
        toggle.classList.add("rotated");
        loader.style.display = "block";

        // Fetch question type data
        fetch("/wpmain/getChapterContent?chapterId=" + currentChapterId + "&loadType=questionType:" + qType)
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                loader.style.display = "none";

                if (qType === "Multiple Choice Questions" && data.questionTypeData && data.questionTypeData.mcqQuestions) {
                    // Store MCQ data globally for button access
                    if (!chapterData[currentChapterId].questionBank) {
                        chapterData[currentChapterId].questionBank = {};
                    }
                    chapterData[currentChapterId].questionBank.mcqQuestions = data.questionTypeData.mcqQuestions;
                    questionsContainer.innerHTML = createMCQSectionContent(data.questionTypeData.mcqQuestions);
                } else if (data.questionTypeData && data.questionTypeData.qnaQuestions && data.questionTypeData.qnaQuestions[qType]) {
                    questionsContainer.innerHTML = createQuestionTypeSectionContent(data.questionTypeData.qnaQuestions[qType]);
                } else {
                    questionsContainer.innerHTML = "<div class=\"no-data-message\">" +
                        "<div class=\"no-data-icon\"><i class=\"fas fa-info-circle\"></i></div>" +
                        "<p>No questions available for this type.</p>" +
                        "</div>";
                }

                // Render math formulas for the newly loaded content
                renderMathInElement(questionsContainer);
            })
            .catch(function(error) {
                console.error("Error loading question type data:", error);
                loader.style.display = "none";
                questionsContainer.innerHTML = "<div class=\"error-message\">" +
                    "<div class=\"error-icon\"><i class=\"fas fa-exclamation-triangle\"></i></div>" +
                    "<p>Error loading questions. Please try again.</p>" +
                    "</div>";
            });
    }

    // Toggle question type content visibility
    function toggleQuestionTypeContent(content, toggle) {
        if (content.classList.contains("open")) {
            content.classList.remove("open");
            toggle.classList.remove("rotated");
        } else {
            content.classList.add("open");
            toggle.classList.add("rotated");
        }
    }

    // Display chapter summary
    function displayChapterSummary(questionTypeCounts) {
        var navContainer = document.getElementById("questionTypeNav");
        var html = "";

        // Add Exercise Solutions
        var exerciseCount = chapterData[currentChapterId].exerciseSolutions.length;
        if (exerciseCount > 0) {
            html += "<div class=\"type-nav-item\" onclick=\"scrollToSection('exerciseSolutionsSection')\">" +
                "<div class=\"type-nav-icon icon-exercise\"><i class=\"fas fa-pencil-alt\"></i></div>" +
                "<div class=\"type-nav-content\">" +
                "<div class=\"type-nav-title\">Exercise Solutions</div>" +
                "<div class=\"type-nav-count\">" + exerciseCount + " questions</div>" +
                "</div>" +
                "</div>";
        }

        // Add question types with different colored icons in specified order
        var iconClasses = {
            "LongAnswer": "icon-long-answer",
            "ShortAnswer": "icon-short-answer",
            "VeryShortAnswer": "icon-very-short",
            "AssertionReason": "icon-assertion",
            "Problem": "icon-problem",
            "Multiple Choice Questions": "icon-mcq",
            "FillBlank": "icon-fill-blank",
            "TrueFalse": "icon-true-false",
            "MatchFollowing": "icon-match",
            "ArrangeSequence": "icon-sequence"
        };

        // Define the order of question types
        var questionTypeOrder = [
            "LongAnswer",
            "ShortAnswer",
            "VeryShortAnswer",
            "AssertionReason",
            "Problem",
            "Multiple Choice Questions",
            "FillBlank",
            "TrueFalse",
            "MatchFollowing",
            "ArrangeSequence"
        ];

        questionTypeOrder.forEach(function(qType) {
            if (questionTypeCounts[qType] && questionTypeCounts[qType] > 0) {
                var typeInfo = questionTypeMap[qType];
                var iconClass = iconClasses[qType] || "icon-exercise";
                if (typeInfo) {
                    html += "<div class=\"type-nav-item\" onclick=\"scrollToQuestionType('" + qType + "')\">" +
                        "<div class=\"type-nav-icon " + iconClass + "\"><i class=\"" + typeInfo.icon + "\"></i></div>" +
                        "<div class=\"type-nav-content\">" +
                        "<div class=\"type-nav-title\">" + typeInfo.title + "</div>" +
                        "<div class=\"type-nav-count\">" + questionTypeCounts[qType] + " questions</div>" +
                        "</div>" +
                        "</div>";
                }
            }
        });

        if (html === "") {
            html = "<div class=\"no-data-message\">" +
                "<div class=\"no-data-icon\"><i class=\"fas fa-info-circle\"></i></div>" +
                "<p>No content available for this chapter.</p>" +
                "</div>";
        }

        navContainer.innerHTML = html;
    }

    // Create content for question type sections (for lazy loading)
    function createQuestionTypeSectionContent(questions) {
        var html = "";
        questions.forEach(function(question, index) {
            html += "<div class=\"question-item\">" +
                "<div class=\"question-text\" id=\"question-" + question.id + "\"><span class=\"question-number\">Q" + (index + 1) + ".</span> " + question.question + "</div>" +
                "<div class=\"answer-text\" id=\"answer-" + question.id + "\">" + question.answer + "</div>" +
                "<div class=\"question-actions\">" +
                "<button class=\"action-btn btn-explanation\" onclick=\"toggleExplanation(" + question.id + ", this)\">" +
                "<i class=\"fas fa-lightbulb\"></i> Show Explanation" +
                "</button>" +
                "<button class=\"action-btn btn-create-more\" onclick=\"createMore(" + question.id + ")\">" +
                "<i class=\"fas fa-plus\"></i> Create More" +
                "</button>" +
                "<button class=\"action-btn btn-ask-doubt\" onclick=\"askDoubt(" + question.id + ")\">" +
                "<i class=\"fas fa-question-circle\"></i> Ask Doubt" +
                "</button>" +
                "<button class=\"action-btn btn-feedback\" onclick=\"openFeedbackModal(" + question.id + ")\">" +
                "<i class=\"fas fa-flag\"></i> Report Issue";
            <%if(gptManager){%>
            html += "<button id=\"fix-btn-" + question.id + "\" class=\"action-btn btn-feedback\" onclick=\"fixQuestion(" + question.id + ",'subjective')\">" +
                "<i class=\"fas fa-wrench\"></i> Fix Question" +
                "</button>" +
                "<button id=\"delete-btn-" + question.id + "\" class=\"action-btn btn-danger\" onclick=\"deleteQuestion(" + question.id + ")\">" +
                "<i class=\"fas fa-trash\"></i> Delete Question" +
                "</button>";
            <%}%>
            html += "</button>" +
                "</div>" +
                "<div class=\"explanation-container\" id=\"explanation-" + question.id + "\">" +
                "<div class=\"explanation-header\">" +
                "<h4 class=\"explanation-title\"><i class=\"fas fa-info-circle\"></i> Explanation</h4>" +
                "<button class=\"close-explanation\" onclick=\"closeExplanation(" + question.id + ")\">" +
                "<i class=\"fas fa-times\"></i>" +
                "</button>" +
                "</div>" +
                "<div class=\"explanation-text\" id=\"explanation-text-" + question.id + "\"></div>" +
                "</div>" +
                "</div>";
        });
        return html;
    }

    // Create content for MCQ sections (for lazy loading)
    function createMCQSectionContent(questions) {
        var html = "<div class=\"mcq-header-actions\">" +
            "<button class=\"mcq-header-btn mcq-btn-play\" onclick=\"playMCQ()\">" +
            "<i class=\"fas fa-play\"></i> Play" +
            "</button>" +
            "<button class=\"mcq-header-btn mcq-btn-practice\" onclick=\"practiceMCQ()\">" +
            "<i class=\"fas fa-dumbbell\"></i> Practice" +
            "</button>" +
            "<button class=\"mcq-header-btn mcq-btn-test\" onclick=\"testMCQ()\">" +
            "<i class=\"fas fa-clipboard-check\"></i> Test" +
            "</button>" +
            "</div>";

        questions.forEach(function(question, index) {
            html += "<div class=\"question-item\">" +
                "<div class=\"question-text\" id=\"question-" + question.id + "\"><span class=\"question-number\">Q" + (index + 1) + ".</span> " + question.question + "</div>";

            // Add MCQ options
            if (question.option1 || question.option2 || question.option3 || question.option4 || question.option5) {
                html += "<div class=\"mcq-options\">";
                if (question.option1) {
                    var isCorrect = question.answer1 === "Yes" ? " correct" : "";
                    html += "<div class=\"mcq-option" + isCorrect + "\">" +
                        "<span class=\"option-label\">A)</span> " + question.option1 + "</div>";
                }
                if (question.option2) {
                    var isCorrect = question.answer2 === "Yes" ? " correct" : "";
                    html += "<div class=\"mcq-option" + isCorrect + "\">" +
                        "<span class=\"option-label\">B)</span> " + question.option2 + "</div>";
                }
                if (question.option3) {
                    var isCorrect = question.answer3 === "Yes" ? " correct" : "";
                    html += "<div class=\"mcq-option" + isCorrect + "\">" +
                        "<span class=\"option-label\">C)</span> " + question.option3 + "</div>";
                }
                if (question.option4) {
                    var isCorrect = question.answer4 === "Yes" ? " correct" : "";
                    html += "<div class=\"mcq-option" + isCorrect + "\">" +
                        "<span class=\"option-label\">D)</span> " + question.option4 + "</div>";
                }
                if (question.option5) {
                    var isCorrect = question.answer5 === "Yes" ? " correct" : "";
                    html += "<div class=\"mcq-option" + isCorrect + "\">" +
                        "<span class=\"option-label\">E)</span> " + question.option5 + "</div>";
                }
                html += "</div>";
            }

            html += "<div class=\"question-actions\">" +
                "<button class=\"action-btn btn-explanation\" onclick=\"toggleExplanation(" + question.id + ", this)\">" +
                "<i class=\"fas fa-lightbulb\"></i> Show Explanation" +
                "</button>" +
                "<button class=\"action-btn btn-create-more\" onclick=\"createMore(" + question.id + ")\">" +
                "<i class=\"fas fa-plus\"></i> Create More" +
                "</button>" +
                "<button class=\"action-btn btn-ask-doubt\" onclick=\"askDoubt(" + question.id + ")\">" +
                "<i class=\"fas fa-question-circle\"></i> Ask Doubt" +
                "</button>" +
                "<button class=\"action-btn btn-feedback\" onclick=\"openFeedbackModal(" + question.id + ")\">" +
                "<i class=\"fas fa-flag\"></i> Report Issue";
            <%if(gptManager){%>
            html += "<button id=\"fix-btn-" + question.id + "\" class=\"action-btn btn-feedback\" onclick=\"fixQuestion(" + question.id + ",'mcq')\">" +
                "<i class=\"fas fa-wrench\"></i> Fix Question" +
                "</button>" +
                "<button id=\"delete-btn-" + question.id + "\" class=\"action-btn btn-danger\" onclick=\"deleteQuestion(" + question.id + ")\">" +
                "<i class=\"fas fa-trash\"></i> Delete Question" +
                "</button>";
            <%}%>
            html += "</button>" +
                "</div>" +
                "<div class=\"explanation-container\" id=\"explanation-" + question.id + "\">" +
                "<div class=\"explanation-header\">" +
                "<h4 class=\"explanation-title\"><i class=\"fas fa-info-circle\"></i> Explanation</h4>" +
                "<button class=\"close-explanation\" onclick=\"closeExplanation(" + question.id + ")\">" +
                "<i class=\"fas fa-times\"></i>" +
                "</button>" +
                "</div>" +
                "<div class=\"explanation-text\" id=\"explanation-text-" + question.id + "\"></div>" +
                "</div>" +
                "</div>";
        });
        return html;
    }

    // Display exercise solutions
    function displayExerciseSolutions(exerciseSolutions) {
        var container = document.getElementById("exerciseSolutionsContainer");
        var section = document.getElementById("exerciseSolutionsSection");
        var html = "";

        if (exerciseSolutions.length === 0) {
            // Hide the entire Exercise Solutions section when there's no data
            section.style.display = "none";
            return;
        } else {
            // Show the section when there's data
            section.style.display = "block";

            exerciseSolutions.forEach(function(question, index) {
                html += "<div class=\"question-item\">" +
                    "<div class=\"question-text\" id=\"question-" + question.id + "\"><span class=\"question-number\">Q" + (index + 1) + ".</span> " + question.question + "</div>" +
                    "<div class=\"answer-text\" id=\"answer-" + question.id + "\">" + question.answer + "</div>" +
                    "<div class=\"question-actions\">" +
                    "<button class=\"action-btn btn-explanation\" onclick=\"toggleExplanation(" + question.id + ", this)\">" +
                    "<i class=\"fas fa-lightbulb\"></i> Show Explanation" +
                    "</button>" +
                    "<button class=\"action-btn btn-create-more\" onclick=\"createMore(" + question.id + ")\">" +
                    "<i class=\"fas fa-plus\"></i> Create More" +
                    "</button>" +
                    "<button class=\"action-btn btn-ask-doubt\" onclick=\"askDoubt(" + question.id + ")\">" +
                    "<i class=\"fas fa-question-circle\"></i> Ask Doubt" +
                    "</button>" +
                    "<button class=\"action-btn btn-feedback\" onclick=\"openFeedbackModal(" + question.id + ")\">" +
                    "<i class=\"fas fa-flag\"></i> Report Issue" ;
                <%if(gptManager){%>
                html += "<button id=\"fix-btn-" + question.id + "\" class=\"action-btn btn-feedback\" onclick=\"fixQuestion(" + question.id + ",'subjective')\">" +
                    "<i class=\"fas fa-wrench\"></i> Fix Question" +
                    "</button>" +
                    "<button id=\"delete-btn-" + question.id + "\" class=\"action-btn btn-danger\" onclick=\"deleteQuestion(" + question.id + ")\">" +
                    "<i class=\"fas fa-trash\"></i> Delete Question" +
                    "</button>" ;
                <%}%>
                html +=    "</button>" +
                    "</div>" +
                    "<div class=\"explanation-container\" id=\"explanation-" + question.id + "\">" +
                    "<div class=\"explanation-header\">" +
                    "<h4 class=\"explanation-title\"><i class=\"fas fa-info-circle\"></i> Explanation</h4>" +
                    "<button class=\"close-explanation\" onclick=\"closeExplanation(" + question.id + ")\">" +
                    "<i class=\"fas fa-times\"></i>" +
                    "</button>" +
                    "</div>" +
                    "<div class=\"explanation-text\" id=\"explanation-text-" + question.id + "\"></div>" +
                    "</div>" +
                    "</div>";
            });
        }

        container.innerHTML = html;
    }

    // Legacy function - now handled by lazy loading
    function displayQuestionBank(questionBank) {
        // This function is no longer used - question bank is now loaded lazily
        console.log("displayQuestionBank called - now using lazy loading");
    }

    // Legacy function - now handled by lazy loading content creation functions
    function createQuestionTypeSection(qType, typeInfo, questions) {
        console.log("Legacy createQuestionTypeSection called - now using lazy loading");
        return "";
    }

    // Legacy function - now handled by lazy loading content creation functions
    function createMCQSection(typeInfo, questions) {
        console.log("Legacy createMCQSection called - now using lazy loading");
        return "";
    }

    // Toggle question type section (legacy function - now redirects to lazy loading)
    function toggleQuestionType(sectionId) {
        // Extract question type from section ID
        var qType;
        if (sectionId === "section-mcq") {
            qType = "Multiple Choice Questions";
        } else {
            qType = sectionId.replace("section-", "").replace(/-/g, " ");
            // Capitalize first letter of each word
            qType = qType.replace(/\b\w/g, function(l) { return l.toUpperCase(); });
        }

        // Use the new lazy loading function
        loadQuestionTypeData(qType, sectionId);
    }

    // Toggle explanation
    function toggleExplanation(questionId, buttonElement) {
        var explanationContainer = document.getElementById("explanation-" + questionId);
        var explanationText = document.getElementById("explanation-text-" + questionId);

        if (explanationContainer.style.display === "block") {
            explanationContainer.style.display = "none";
            buttonElement.innerHTML = "<i class=\"fas fa-lightbulb\"></i> Show Explanation";
        } else {
            // Show loading spinner
            explanationText.innerHTML = "<div style=\"text-align: center; padding: 20px;\">" +
                "<i class=\"fas fa-spinner fa-spin\"></i> Loading explanation..." +
                "</div>";
            explanationContainer.style.display = "block";
            buttonElement.innerHTML = "<i class=\"fas fa-eye-slash\"></i> Hide Explanation";

            // Fetch explanation
            fetch("/wpmain/getExplanation?questionId=" + questionId)
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    explanationText.innerHTML = data.explanation || "No explanation available.";

                    // Scroll to explanation within the panel container
                    var panelContent = document.querySelector('.panel-content');
                    if (panelContent) {
                        // Get the position of the explanation relative to the panel content
                        var explanationRect = explanationContainer.getBoundingClientRect();
                        var panelRect = panelContent.getBoundingClientRect();
                        var scrollPosition = panelContent.scrollTop + explanationRect.top - panelRect.top - 20; // 20px offset for better visibility

                        // Scroll the panel content container smoothly
                        panelContent.scrollTo({
                            top: scrollPosition,
                            behavior: "smooth"
                        });
                    }

                    // Render math formulas
                    renderMathFormulas();
                })
                .catch(function(error) {
                    console.error("Error loading explanation:", error);
                    explanationText.innerHTML = "Error loading explanation.";
                });
        }
    }

    // Close explanation
    function closeExplanation(questionId) {
        var explanationContainer = document.getElementById("explanation-" + questionId);
        var button = document.querySelector("[onclick*=\"toggleExplanation(" + questionId + "\"]");

        explanationContainer.style.display = "none";
        if (button) {
            button.innerHTML = "<i class=\"fas fa-lightbulb\"></i> Show Explanation";
        }
    }

    // Feedback Modal Functions
    var currentQuestionId = null;

    function openFeedbackModal(questionId) {
        currentQuestionId = questionId;
        document.getElementById("feedbackModal").style.display = "block";

        // Reset form
        var radioButtons = document.querySelectorAll('input[name="issueType"]');
        radioButtons.forEach(function(radio) {
            radio.checked = false;
        });
        document.getElementById("feedbackDetails").value = "";
    }

    function closeFeedbackModal() {
        document.getElementById("feedbackModal").style.display = "none";
        currentQuestionId = null;
    }

    function submitFeedback() {
        if (!currentQuestionId) {
            showToast("Error: No question selected");
            return;
        }

        // Get selected issue type
        var selectedIssue = "";
        var radioButtons = document.querySelectorAll('input[name="issueType"]');
        radioButtons.forEach(function(radio) {
            if (radio.checked) {
                selectedIssue = radio.value;
            }
        });

        // Get feedback details
        var feedbackDetails = document.getElementById("feedbackDetails").value.trim();

        // Validate that at least one field is filled
        if (!selectedIssue && !feedbackDetails) {
            showToast("Please select an issue type or provide feedback details");
            return;
        }

        // Disable submit button
        var submitBtn = document.querySelector(".feedback-btn-submit");
        submitBtn.disabled = true;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Submitting...";

        // Make AJAX call using RemoteFunction approach
        var params = "id=" + encodeURIComponent(currentQuestionId) +
            "&issuesList=" + encodeURIComponent(selectedIssue) +
            "&issue=" + encodeURIComponent(feedbackDetails);

        var xhr = new XMLHttpRequest();
        xhr.open("POST", "/log/addQuizIssue", true);
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = "Submit Feedback";

                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.status === "OK") {
                            showToast("Feedback submitted successfully! Thank you for your input.");
                            closeFeedbackModal();
                        } else {
                            showToast("Error submitting feedback. Please try again.");
                        }
                    } catch (e) {
                        console.error("Error parsing response:", e);
                        showToast("Error submitting feedback. Please try again.");
                    }
                } else {
                    showToast("Error submitting feedback. Please try again.");
                }
            }
        };

        xhr.send(params);
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        var modal = document.getElementById("feedbackModal");
        if (event.target === modal) {
            closeFeedbackModal();
        }
    };

    // Utility functions
    function showLoading() {
        document.getElementById("loadingSpinner").style.display = "block";
    }

    function hideLoading() {
        document.getElementById("loadingSpinner").style.display = "none";
    }

    function hideAllSections() {
        document.getElementById("welcomeMessage").style.display = "none";
        document.getElementById("chapterSummary").style.display = "none";
        document.getElementById("exerciseSolutionsSection").style.display = "none";
        document.getElementById("questionBankSection").style.display = "none";
    }

    function scrollToSection(sectionId) {
        var element = document.getElementById(sectionId);
        var panelContent = document.querySelector('.panel-content');

        if (element && panelContent) {
            // Get the position of the element relative to the panel content
            var elementRect = element.getBoundingClientRect();
            var panelRect = panelContent.getBoundingClientRect();
            var scrollPosition = panelContent.scrollTop + elementRect.top - panelRect.top - 20; // 20px offset for better visibility

            // Scroll the panel content container smoothly
            panelContent.scrollTo({
                top: scrollPosition,
                behavior: "smooth"
            });
        }
    }

    function scrollToQuestionType(qType) {
        var sectionId;

        // Handle MCQ questions with special ID
        if (qType === "Multiple Choice Questions") {
            sectionId = "section-mcq";
        } else {
            sectionId = "section-" + qType.replace(/\s+/g, "-");
        }

        var element = document.getElementById(sectionId);
        if (element) {
            // Load the section data if not already loaded, then scroll
            loadQuestionTypeData(qType, sectionId);

            // Scroll to the section within the panel container
            setTimeout(function() {
                var panelContent = document.querySelector('.panel-content');
                if (panelContent) {
                    // Get the position of the element relative to the panel content
                    var elementRect = element.getBoundingClientRect();
                    var panelRect = panelContent.getBoundingClientRect();
                    var scrollPosition = panelContent.scrollTop + elementRect.top - panelRect.top - 20; // 20px offset for better visibility

                    // Scroll the panel content container smoothly
                    panelContent.scrollTo({
                        top: scrollPosition,
                        behavior: "smooth"
                    });
                }
            }, 100);
        }
    }

    function showToast(message) {
        var toast = document.getElementById("toast");
        toast.textContent = message;
        toast.classList.add("show");

        setTimeout(function() {
            toast.classList.remove("show");
        }, 3000);
    }

    async function createMore(questionId) {
        // Highlight the question card
        highlightQuestionCard(questionId);

        const answer = await createMoreHandler(questionId)

        // Only update UI if it's not a pending request message
        if (answer !== "Processing your request, please wait...") {
            window.chatApp.hideTypingLoader(document.getElementById('chat-messages'))
            window.chatApp.addMessage("bot", answer, document.getElementById('chat-messages'))
        }

        // Remove highlight after response is complete
        setTimeout(() => {
            removeQuestionCardHighlight(questionId);
        }, 1000);
    }

    function askDoubt(questionId) {
        // Highlight the question card
        highlightQuestionCard(questionId);

        // Get the question content
        const questionElement = document.getElementById("question-" + questionId);
        if (!questionElement) {
            showToast("Question not found!");
            removeQuestionCardHighlight(questionId);
            return;
        }

        const questionContent = questionElement.textContent || questionElement.innerText;

        // Use ChatApp's initiateAskDoubt method to open chat and store question content
        if (window.chatApp && typeof window.chatApp.initiateAskDoubt === 'function') {
            window.chatApp.initiateAskDoubt(questionContent);
            // Store the questionId for later highlight removal
            window.chatApp.currentHighlightedQuestionId = questionId;
        } else {
            showToast("Chat functionality not available. Please refresh the page.");
            removeQuestionCardHighlight(questionId);
        }
    }

    // Function to highlight question card
    function highlightQuestionCard(questionId) {
        // Remove any existing highlights first
        removeAllQuestionCardHighlights();

        // Find the question item container
        const questionElement = document.getElementById("question-" + questionId);
        if (questionElement) {
            const questionItem = questionElement.closest('.question-item');
            if (questionItem) {
                questionItem.classList.add('question-item-highlighted');
            }
        }
    }

    // Function to remove highlight from specific question card
    function removeQuestionCardHighlight(questionId) {
        const questionElement = document.getElementById("question-" + questionId);
        if (questionElement) {
            const questionItem = questionElement.closest('.question-item');
            if (questionItem) {
                questionItem.classList.remove('question-item-highlighted');
            }
        }
    }

    // Function to remove all question card highlights
    function removeAllQuestionCardHighlights() {
        const highlightedItems = document.querySelectorAll('.question-item-highlighted');
        highlightedItems.forEach(item => {
            item.classList.remove('question-item-highlighted');
        });
    }

    function playMCQ() {
        // Get MCQ data for current chapter
        if (chapterData[currentChapterId] && chapterData[currentChapterId].questionBank && chapterData[currentChapterId].questionBank.mcqQuestions) {
            var mcqQuestions = chapterData[currentChapterId].questionBank.mcqQuestions;
            if (mcqQuestions.length > 0) {
                var quizId = mcqQuestions[0].quizId;
                var resId = mcqQuestions[0].resId;
                //change the url to /prepjoy/prepJoyGame?quizId=7261&resId=39831&quizType=&source=web&siteName=books&learn=false&pubDesk=false&dailyTest=false
                var url = "/prepjoy/prepJoyGame?quizId=" + quizId + "&resId=" + resId + "&quizType=&source=web&siteName="+siteName+"&learn=false&pubDesk=false&dailyTest=false";
                window.open(url, "_blank");
            } else {
                showToast("No MCQ questions available for this chapter");
            }
        } else {
            // Try to load MCQ data first
            loadQuestionTypeData("Multiple Choice Questions", "section-mcq");
            showToast("Loading MCQ data... Please try again in a moment.");
        }
    }

    function practiceMCQ() {
        // Get MCQ data for current chapter
        if (chapterData[currentChapterId] && chapterData[currentChapterId].questionBank && chapterData[currentChapterId].questionBank.mcqQuestions) {
            var mcqQuestions = chapterData[currentChapterId].questionBank.mcqQuestions;
            if (mcqQuestions.length > 0) {
                var quizId = mcqQuestions[0].quizId;
                var resId = mcqQuestions[0].resId;
                 //change the url to /prepjoy/prepJoyGame?quizId=7261&resId=39831&quizType=practice&source=web&siteName=books&learn=false&pubDesk=false&dailyTest=false
                var url = "/prepjoy/prepJoyGame?quizId=" + quizId + "&resId=" + resId + "&quizType=practice&source=web&siteName="+siteName+"&learn=false&pubDesk=false&dailyTest=false";
                window.open(url, "_blank");
            } else {
                showToast("No MCQ questions available for this chapter");
            }
        } else {
            // Try to load MCQ data first
            loadQuestionTypeData("Multiple Choice Questions", "section-mcq");
            showToast("Loading MCQ data... Please try again in a moment.");
        }
    }

    function testMCQ() {
        // Get MCQ data for current chapter
        if (chapterData[currentChapterId] && chapterData[currentChapterId].questionBank && chapterData[currentChapterId].questionBank.mcqQuestions) {
            var mcqQuestions = chapterData[currentChapterId].questionBank.mcqQuestions;
            if (mcqQuestions.length > 0) {
                var quizId = mcqQuestions[0].quizId;
                var resId = mcqQuestions[0].resId;
                //change the url to /prepjoy/prepJoyGame?quizId=7261&resId=39831&quizType=testSeries&source=web&siteName=books&learn=false&pubDesk=false&dailyTest=false
                var url = "/prepjoy/prepJoyGame?quizId=" + quizId + "&resId=" + resId + "&quizType=testSeries&source=web&siteName="+siteName+"&learn=false&pubDesk=false&dailyTest=false";
                window.open(url, "_blank");
            } else {
                showToast("No MCQ questions available for this chapter");
            }
        } else {
            // Try to load MCQ data first
            loadQuestionTypeData("Multiple Choice Questions", "section-mcq");
            showToast("Loading MCQ data... Please try again in a moment.");
        }
    }

    // Math formula rendering
    function renderMathFormulas() {
        if (typeof renderMathInElement === "function") {
            var contentArea = document.querySelector(".content-area");
            renderMathInElement(contentArea, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\(", right: "\\)", display: false},
                    {left: "\\[", right: "\\]", display: true}
                ]
            });
        } else if (typeof MathJax !== "undefined" && MathJax.Hub && MathJax.Hub.Queue) {
            MathJax.Hub.Queue(["Typeset", MathJax.Hub, document.querySelector(".content-area")]);
        }
    }

    // Scroll handling for go-to-top button
    function handleScroll() {
        var goToTopBtn = document.getElementById("goToTopBtn");
        var panelContent = document.querySelector('.panel-content');
        var scrollTop = 0;

        if (panelContent) {
            scrollTop = panelContent.scrollTop;
        } else {
            // Fallback to window scroll if panel not found
            scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        }

        if (scrollTop > 350) {
            goToTopBtn.classList.add("visible");

            setTimeout(function (){
                goToTopBtn.classList.remove("visible");
            }, 3000);
        } else {
            goToTopBtn.classList.remove("visible");
        }
    }

    // Scroll to top function
    function scrollToTop() {
        var panelContent = document.querySelector('.panel-content');
        if (panelContent) {
            // Scroll the panel content container to top
            panelContent.scrollTo({
                top: 0,
                behavior: "smooth"
            });
        } else {
            // Fallback to window scroll if panel not found
            window.scrollTo({
                top: 0,
                behavior: "smooth"
            });
        }
    }

    // Function to open book details page
    function openBookDtlPage() {
        var bookId = "${booksMst.id}";
        window.location.href = "/wpmain/aiBookDtl?bookId=" + bookId;
    }

    function fixQuestion(objId, questionType) {
        // Get the button element
        var fixBtn = document.getElementById("fix-btn-" + objId);

        // Show loader and disable button
        if (fixBtn) {
            fixBtn.disabled = true;
            fixBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Fixing...";
        }

        var params = "objId=" + encodeURIComponent(objId) + "&questionType=" + encodeURIComponent(questionType);
        <g:remoteFunction controller="autogpt" action="fixQuestion"  onSuccess='fixQuestionDone(data);' onFailure='fixQuestionError(data);' params="params"></g:remoteFunction>
    }
    function fixQuestionDone(data) {
        var objectiveMst = data.objectiveMst;
        var questionType = data.questionType;
        var id = objectiveMst.id;

        // Hide loader and re-enable button
        var fixBtn = document.getElementById("fix-btn-" + id);
        if (fixBtn) {
            fixBtn.disabled = false;
            fixBtn.innerHTML = "<i class=\"fas fa-wrench\"></i> Fix Question";
        }

        if("mcq"==questionType){
            //update the fields based on id
            document.getElementById("question-"+id).innerHTML = objectiveMst.question;
            document.getElementById("option-label-1-"+id).innerHTML = objectiveMst.option1;
            document.getElementById("option-label-2-"+id).innerHTML = objectiveMst.option2;
            document.getElementById("option-label-3-"+id).innerHTML = objectiveMst.option3;
            document.getElementById("option-label-4-"+id).innerHTML = objectiveMst.option4;
            renderMathFormulas();
        }else{
            document.getElementById("question-"+id).innerHTML = objectiveMst.question;
            document.getElementById("answer-"+id).innerHTML = objectiveMst.answer;
            renderMathFormulas();
        }
        console.log(objectiveMst);
    }

    function fixQuestionError(data) {
        // Find the button that was clicked by checking all fix buttons
        var fixBtns = document.querySelectorAll("[id^='fix-btn-']");
        for (var i = 0; i < fixBtns.length; i++) {
            var btn = fixBtns[i];
            if (btn.disabled && btn.innerHTML.includes("Fixing...")) {
                btn.disabled = false;
                btn.innerHTML = "<i class=\"fas fa-wrench\"></i> Fix Question";
                break;
            }
        }
        console.error("Error fixing question:", data);
        showToast("Error fixing question. Please try again.");
    }

    function deleteQuestion(objId) {
        // Show confirmation dialog
        if (!confirm("Are you sure you want to delete this question? This action cannot be undone.")) {
            return;
        }

        // Get the button element
        var deleteBtn = document.getElementById("delete-btn-" + objId);

        // Show loader and disable button
        if (deleteBtn) {
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Deleting...";
        }

        var params = "objId=" + encodeURIComponent(objId);
        <g:remoteFunction controller="wpmain" action="deleteQuestion" onSuccess='deleteQuestionDone(data);' onFailure='deleteQuestionError(data);' params="params"></g:remoteFunction>
    }

    function deleteQuestionDone(data) {
        var objId = data.objId;
        var success = data.success;

        if (success) {
            // Remove the question item from the DOM
            var questionItem = document.getElementById("delete-btn-" + objId).closest('.question-item');
            if (questionItem) {
                questionItem.remove();
                showToast("Question deleted successfully!");

                // Optionally reload the chapter to update counts
                // loadChapter(currentChapterId);
            }
        } else {
            // Re-enable button on failure
            var deleteBtn = document.getElementById("delete-btn-" + objId);
            if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = "<i class=\"fas fa-trash\"></i> Delete Question";
            }
            showToast("Error deleting question: " + (data.message || "Unknown error"));
        }
    }

    function deleteQuestionError(data) {
        // Find the button that was clicked by checking all delete buttons
        var deleteBtns = document.querySelectorAll("[id^='delete-btn-']");
        for (var i = 0; i < deleteBtns.length; i++) {
            var btn = deleteBtns[i];
            if (btn.disabled && btn.innerHTML.includes("Deleting...")) {
                btn.disabled = false;
                btn.innerHTML = "<i class=\"fas fa-trash\"></i> Delete Question";
                break;
            }
        }
        console.error("Error deleting question:", data);
        showToast("Error deleting question. Please try again.");
    }

    // Function to determine question type and section based on questionId
    function determineQuestionType(questionId) {
        // Check if question is from Exercise Solutions
        if (chapterData[currentChapterId] && chapterData[currentChapterId].exerciseSolutions) {
            const exerciseQuestion = chapterData[currentChapterId].exerciseSolutions.find(q => q.id == questionId);
            if (exerciseQuestion) {
                return {
                    type: "Exercise Solutions",
                    section: "exerciseSolutions",
                    qType: exerciseQuestion.qType || "Exercise Solutions"
                };
            }
        }

        // Check if question is from Question Bank MCQs
        if (chapterData[currentChapterId] && chapterData[currentChapterId].questionBank && chapterData[currentChapterId].questionBank.mcqQuestions) {
            const mcqQuestion = chapterData[currentChapterId].questionBank.mcqQuestions.find(q => q.id == questionId);
            if (mcqQuestion) {
                return {
                    type: "Multiple Choice Questions",
                    section: "questionBank",
                    qType: "Multiple Choice Questions"
                };
            }
        }

        // Check if question is from Question Bank QnA
        if (chapterData[currentChapterId] && chapterData[currentChapterId].questionBank && chapterData[currentChapterId].questionBank.qnaQuestions) {
            for (const qType in chapterData[currentChapterId].questionBank.qnaQuestions) {
                const questions = chapterData[currentChapterId].questionBank.qnaQuestions[qType];
                const qnaQuestion = questions.find(q => q.id == questionId);
                if (qnaQuestion) {
                    return {
                        type: qType,
                        section: "questionBank",
                        qType: qType
                    };
                }
            }
        }

        // Default fallback
        return {
            type: "Unknown",
            section: "unknown",
            qType: "Unknown"
        };
    }

    // Function to generate appropriate prompts based on question type
    function generatePromptsForQuestionType(questionTypeInfo, questionId) {
        const questionElement = document.getElementById("question-" + questionId);
        if (!questionElement) {
            console.error("Question element not found for ID:", questionId);
            return {
                systemPrompt: "Create more questions",
                userPrompt: "Create more questions"
            };
        }

        const questionContent = questionElement.textContent;
        let answerContent = "";

        // Handle different question types with different DOM structures
        if (questionTypeInfo.type === "Multiple Choice Questions") {
            // For MCQ, collect all options instead of looking for answer element
            let options = [];
            for (let i = 1; i <= 5; i++) {
                const optionElement = document.getElementById("option-label-" + i + "-" + questionId);
                if (optionElement) {
                    // Get the parent mcq-option div to get the full option text
                    const optionDiv = optionElement.closest('.mcq-option');
                    if (optionDiv) {
                        const optionText = optionDiv.textContent.trim();
                        options.push(optionText);

                        // Check if this option is marked as correct
                        if (optionDiv.classList.contains('correct')) {
                            answerContent = "Correct answer: " + optionText;
                        }
                    }
                }
            }

            if (options.length > 0) {
                answerContent = answerContent || "Options: " + options.join(", ");
            } else {
                answerContent = "Multiple choice options";
            }
        } else {
            // For other question types, try to get the answer element
            const answerElement = document.getElementById("answer-" + questionId);
            if (answerElement) {
                answerContent = answerElement.textContent;
            } else {
                // Fallback for question types that might not have answer elements
                answerContent = "Answer content";
            }
        }

        let systemPrompt = "";
        let userPrompt = "";

        switch (questionTypeInfo.type) {
            case "Exercise Solutions":
                systemPrompt = "Create more Exercise Solutions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "Multiple Choice Questions":
                systemPrompt = "Create more Multiple Choice Questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "LongAnswer":
                systemPrompt = "Create more Long Answer Type questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "ShortAnswer":
                systemPrompt = "Create more Short Answer Type questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "VeryShortAnswer":
                systemPrompt = "Create more Very Short Answer Type questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "AssertionReason":
                systemPrompt = "Create more Assertion / Reasoning Type questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "Problem":
                systemPrompt = "Create more Problem questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "FillBlank":
                systemPrompt = "Create more Fill in the Blanks questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "TrueFalse":
                systemPrompt = "Create more True or False questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "MatchFollowing":
                systemPrompt = "Create more Match the Following questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            case "ArrangeSequence":
                systemPrompt = "Create more Arrange in Right Sequence questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;

            default:
                systemPrompt = "Create more questions like the following question and answer:\n" +
                              questionContent + "\n" + answerContent;
                userPrompt = "Create more questions";
                break;
        }

        return {
            systemPrompt: systemPrompt,
            userPrompt: userPrompt
        };
    }

    async function createMoreHandler(questionId) {
        // Determine question type and section
        const questionTypeInfo = determineQuestionType(questionId);

        // Generate appropriate prompts based on question type
        const prompts = generatePromptsForQuestionType(questionTypeInfo, questionId);

        window.chatApp.addMessage("user", prompts.userPrompt, document.getElementById('chat-messages'));

        window.chatApp.showTypingLoader(document.getElementById('chat-messages'))

        // Update send button to spinner and set response as incomplete (waiting for API response)
        window.chatApp.updateSendButtonToSpinner();
        window.chatApp.isResponseComplete = false;

        if(document.getElementById('toggle-btn').classList.contains("active")){
            window.chatApp.closeToggle('toggle-btn')()
        }

        const queryConstructObj = {
            questionId: questionId,
            systemPrompt: prompts.systemPrompt,
            userPrompt: prompts.userPrompt,
            questionType: questionTypeInfo.type,
            section: questionTypeInfo.section
        }

        const queryContent = constructQuery(queryConstructObj);
        const bookId = "${booksMst.id}";
        const requestObj = {
            bookId: bookId,
            chapterId: currentChapterId,
            resId: resId,
            namespace: namespace,
            query: queryContent,
            chatHistory: window.chatApp ? window.chatApp.chatHistory : [],
            resType:"userInput",
        }
        return await retrieveDataHandler(requestObj, queryConstructObj.systemPrompt, queryConstructObj.userPrompt)
    }

    function constructQuery(queryConstructObj) {
        // The systemPrompt already contains the question and answer content
        // So we just combine systemPrompt and userPrompt
        return queryConstructObj.systemPrompt + "\n" + queryConstructObj.userPrompt;
    }

    async function retrieveDataHandler(requestObj, systemPrompt, userPrompt) {
        // Check if storePdfVectors is in progress
        if (isStorePdfVectorsInProgress) {
            // Store the request to be processed later
            pendingRetrieveDataRequest = {
                requestObj: requestObj,
                systemPrompt: systemPrompt,
                userPrompt: userPrompt
            };
            console.log("storePdfVectors is in progress, request will be processed after completion");
            showToast("Preparing content... Your request will be processed shortly.");
            return "Processing your request, please wait...";
        }
        if(requestObj.namespace === null){
            requestObj.namespace = namespace
        }

        try {
            // Create AbortController for this request
            const controller = new AbortController();
            activeRetrieveDataControllers.add(controller);

            const response = await fetch('/prompt/retrieveData',{
                method:"POST",
                body:JSON.stringify(requestObj),
                headers:{
                    "Content-Type":"application/json"
                },
                signal: controller.signal
            })

            // Remove controller from active set when request completes
            activeRetrieveDataControllers.delete(controller);

            const answer = await response.json()
            saveResponse(answer, requestObj.query, systemPrompt, userPrompt)
            // Update chat history with the user message
            if (window.chatApp && window.chatApp.chatHistory) {
                const hisObj = {
                    ai:answer.answer,
                    user:userPrompt
                }
                window.chatApp.chatHistory.push(hisObj);
            }
            return answer.answer
        }catch (err){
            // Remove controller from active set on error
            if (err.name === 'AbortError') {
                console.log('Request was aborted due to chapter change');
                return "Request cancelled";
            }

            console.log(err)
            if (window.chatApp) {
                window.chatApp.addMessage("bot", "Sorry, there was an error processing your request. Please try again.", document.getElementById('chat-messages'));
                window.chatApp.hideTypingLoader();
                window.chatApp.isResponseComplete = true;
                window.chatApp.updateSendButtonToSend();
            }
        }
    }

    // Make retrieveDataHandler available globally for ChatApp.js
    window.retrieveDataHandler = retrieveDataHandler;

    async function saveResponse(answer, query, systemPrompt, userPrompt){
        const logUserQuery = {
            username: username,
            response: answer.answer,
            resId: resId,
            readingMaterialResId: resId,
            promptType: "userInput",
            userPrompt: userPrompt,
            systemPrompt: systemPrompt
        }

        try {
            const storeUserQuery = await fetch('/gptLog/save ',{
                method:"POST",
                body:JSON.stringify(logUserQuery),
                headers:{
                    "Content-Type":"application/json"
                }
            })
            if(storeUserQuery.ok){
                return true
            }else{
                return false
            }
        }catch (err){
            console.log(err)
        }
    }

    async function checkAndUpdateNS(namespaceToCheck){
        if(namespaceToCheck && namespaceToCheck!==""){
            return;
        }

        // Set flag to indicate storePdfVectors is in progress
        isStorePdfVectorsInProgress = true;

        const bookId = "${booksMst.id}";
        const namespaceResponse = await fetch("/prompt/storePdfVectors?bookId="+bookId+"&chapterId="+currentChapterId+"&resId="+resId)

        // Reset flag when response is received
        isStorePdfVectorsInProgress = false;

        if(!namespaceResponse.ok){
            alert("Error in storing PDF")
            return
        }
        const namespaceData = await namespaceResponse.json()
        if(namespaceData.status==='OK'){
            namespace = namespaceData.namespace;
        }

        // Process any pending retrieveDataHandler request
        if (pendingRetrieveDataRequest) {
            console.log("Processing pending retrieveDataHandler request");
            const pendingRequest = pendingRetrieveDataRequest;
            pendingRetrieveDataRequest = null; // Clear the pending request

            // Call retrieveDataHandler with the stored parameters
            const answer = await retrieveDataHandler(
                pendingRequest.requestObj,
                pendingRequest.systemPrompt,
                pendingRequest.userPrompt
            );

            // Update the chat UI with the response
            if (window.chatApp && answer && answer !== "Processing your request, please wait...") {
                window.chatApp.hideTypingLoader(document.getElementById('chat-messages'));
                window.chatApp.addMessage("bot", answer, document.getElementById('chat-messages'));

                // Update chat history for chat requests
                if (pendingRequest.userPrompt) {
                    const hisObj = {
                        ai: answer,
                        user: pendingRequest.userPrompt
                    };
                    window.chatApp.chatHistory.push(hisObj);
                }
            }
        }
    }

    // Helper function to process pending retrieveDataHandler requests
    async function processPendingRetrieveDataRequest() {
        if (pendingRetrieveDataRequest && !isStorePdfVectorsInProgress) {
            console.log("Processing pending retrieveDataHandler request");
            const pendingRequest = pendingRetrieveDataRequest;
            pendingRetrieveDataRequest = null; // Clear the pending request

            // Call retrieveDataHandler with the stored parameters
            const answer = await retrieveDataHandler(
                pendingRequest.requestObj,
                pendingRequest.systemPrompt,
                pendingRequest.userPrompt
            );

            // Update the chat UI with the response
            if (window.chatApp && answer && answer !== "Processing your request, please wait...") {
                window.chatApp.hideTypingLoader(document.getElementById('chat-messages'));
                window.chatApp.addMessage("bot", answer, document.getElementById('chat-messages'));

                // Update chat history for chat requests
                if (pendingRequest.userPrompt) {
                    const hisObj = {
                        ai: answer,
                        user: pendingRequest.userPrompt
                    };
                    window.chatApp.chatHistory.push(hisObj);
                }
            }
        }
    }

</script>
