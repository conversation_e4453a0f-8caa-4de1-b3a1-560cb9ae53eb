<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <%

        String displayTitle = "All-India Online Test Series and Best books for CBSE | ICSE | CUET | NEET | JEE | Olympiads | Competitive Exams"
        if(title!=null) displayTitle = title
        else if(session["siteTitle"]!=null) displayTitle = session["siteTitle"]
    %>
    <title><%= displayTitle%></title>

    <%
        String displayDesc = "Buy best school and competitive Exams books and online test series. Best way to prepare for your exams."
        if(seoDesc!=null) displayDesc = seoDesc
        else if(session["siteDescription"]!=null) displayDesc = session["siteDescription"]
        String metaKeywords ="buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book."
        if(keywords!=null) metaKeywords = keywords
        if(session["keywords"]!=null) metaKeywords=session["keywords"]

        String displayOGTitle = "All-India Online Test Series and Best books for CBSE | ICSE | CUET | NEET | JEE | Olympiads | Competitive Exams"
        if(title!=null) displayOGTitle = title
        else if(session["siteTitle"]!=null) displayOGTitle = session["siteTitle"]
    %>
    <meta name="description" content="${displayDesc}">

    <%
        String serverURL = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
    %>

    <meta name="keywords" content="${metaKeywords}" />
    <meta name="generator" content="${session['siteName']} Books" />
    <meta name="author" content="webmaster - ${session['siteName']} Books and Learning Pvt Ltd">
    <meta name="subject" content="Book Store Online : Buy Books Online from ${session['siteName']} Books Store">
    <meta name="keyphrase" content="Medical Exam Books, Board exams books, CBSE Exam Books, UGC Net, Air Force Books, state exam books, Govt Exam Books, NDA Exam Books, Bank Po Books, Entrance Exam Books, Engineering Books, Exam Books, General Books, General English Books, General Knowledge Books, NDA & CDS Books, SBI exam books, competition books in Hindi, ssc competition books, civil service books, banking Exams books, Gate, Teacher exam Books, buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book.">
    <meta name="abstract" content="Find large collection of Entrance Exam Books for engineering, medical, Banking, school and other Exam.">

    <!-- Favicon -->
    <link rel="shortcut icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon"/>
    <link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
    <link rel="android-touch-icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}"/>
    <link rel="windows-touch-icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" />

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" referrerpolicy="no-referrer" async/>

    <asset:stylesheet href="gptsir/templateHeader.css"/>
    <asset:stylesheet href="gptsir/main.css"/>
</head>
<body>

<header>
    <div class="container">
        <div class="header-content">
            <a href="/sp/${session["siteName"]}" class="logo">
                <img src="${assetPath(src: 'gptsirlandingpage/gptsirai-logo.png')}" alt="GPT Sir AI">
            </a>
            <nav class="nav-menu">
                <ul class="nav-links">
                    <li><a href="#categories">Categories</a></li>
                </ul>
                <div class="auth-buttons">
                    <sec:ifNotLoggedIn>
                    <a href="/privatelabel/loginPage?mode=login" class="login-btn">Login</a>
                    </sec:ifNotLoggedIn>
                    <sec:ifLoggedIn>
                    <a href="/wsLibrary/myLibrary?mode=mybooks" class="login-btn">My Books</a>
                    </sec:ifLoggedIn>
                    <a href="/sp/${session["siteName"]}/store" class="signup-btn">Store</a>
                </div>
            </nav>
        </div>
    </div>
</header>
<main>
    <div class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-image">
                    <img src="${assetPath(src: 'gptsirlandingpage/hero-img.jpeg')}" alt="GPT. Sir AI-powered learning interface" class="hero-img">
                </div>
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="highlight">AI</span> Inside Every Book
                    </h1>
                    <h2 class="hero-subtitle-main">
                        Smarter Books. Better Scores.
                    </h2>
                    <p class="hero-description">
                        NCERT, State Boards, Competitive Exams & Higher Edu. GPT Sir books are your all-in-one AI-powered learning companions. Packed with theory, smart notes, massive question banks, and interactive MCQ modes.
                    </p>
                    <div class="hero-cta">
                        <a href="/sp/${session["siteName"]}/store" class="cta-button" target="_blank">Go to Store</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Mind Map Section -->
    <section class="mindmap-section section-bg-pattern-1">
        <div class="container">
            <div class="mindmap-content">
                <h2 class="section-title">Not Just Books <br><span class="highlight">AI Supercharged Learning Tools</span></h2>
            </div>

            <div class="mindmap-container">
                <div class="central-node">
                    <div class="central-content">
                        <div class="central-icon">📚</div>
                        <h3>GPT Sir Books</h3>
                    </div>
                </div>

                <!-- Rotating features container -->
                <div class="features-rotating-container">
                    <div class="feature-node feature-1" data-delay="0">
                        <div class="node-content">
                            <div class="node-icon">💡</div>
                            <h4>Instant Doubt Solving</h4>
                            <p>Ask any question from the chapter and get accurate, curriculum-aligned answers instantly</p>
                        </div>
                    </div>

                    <div class="feature-node feature-2" data-delay="200">
                        <div class="node-content">
                            <div class="node-icon">🗺️</div>
                            <h4>AI-Powered Question Bank</h4>
                            <p>More than 3× the number of questions compared to market guides—solved, tagged, and ready to practice</p>
                        </div>
                    </div>

                    <div class="feature-node feature-3" data-delay="400">
                        <div class="node-content">
                            <div class="node-icon">👩‍🏫</div>
                            <h4>AI Tutor</h4>
                            <p>Learn faster with AI-powered guidance.</p>
                        </div>
                    </div>

                    <div class="feature-node feature-4" data-delay="600">
                        <div class="node-content">
                            <div class="node-icon">🎮</div>
                            <h4>Gamified Learning</h4>
                            <p>Gamified MCQ solving, deliberate practice with hints, and timed testing for exam readiness</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- AI Features Comparison Section -->
    <section class="ai-features-section section-bg-pattern-2">
        <div class="container">
            <h2 class="section-title">
                Built with AI. Reviewed by Experts.<br>
                <span class="highlight">Designed for Exams.</span>
            </h2>
            <div class="ai-features-container">
                <div class="ai-features-content">
                    <div class="features-list">
                        <p class="section-subtitle">GPT Sir book contains:</p>
                        <div class="feature-item" data-delay="0">
                            <div class="feature-icon">✅</div>
                            <div class="feature-text">
                                <h4>Solved Previous Year Questions</h4>
                                <p>From top board and entrance exams</p>
                            </div>
                        </div>

                        <div class="feature-item" data-delay="200">
                            <div class="feature-icon">✅</div>
                            <div class="feature-text">
                                <h4>AI-Predicted New Questions</h4>
                                <p>Based on blueprint analysis and syllabus weightage</p>
                            </div>
                        </div>

                        <div class="feature-item" data-delay="400">
                            <div class="feature-icon">✅</div>
                            <div class="feature-text">
                                <h4>Multiple Question Types</h4>
                                <p>MCQs, short/long answers, case-based, assertion–reason, and more</p>
                            </div>
                        </div>

                        <div class="feature-item" data-delay="600">
                            <div class="feature-icon">✅</div>
                            <div class="feature-text">
                                <h4>Rich Metadata</h4>
                                <p>Tagged by difficulty, concept, Bloom's taxonomy level</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="comparison-table-container">
                    <table class="comparison-table">
                        <thead>
                        <tr>
                            <th>Feature</th>
                            <th>Traditional Guides</th>
                            <th class="gptsir-column">GPT Sir Books</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr class="table-row" data-delay="0">
                            <td class="feature-name">Total Questions</td>
                            <td class="traditional-value">~300–500</td>
                            <td class="gptsir-value">1,500–2,000+</td>
                        </tr>
                        <tr class="table-row" data-delay="100">
                            <td class="feature-name">Content Source</td>
                            <td class="traditional-value">Static past papers</td>
                            <td class="gptsir-value">Past + AI-predicted</td>
                        </tr>
                        <tr class="table-row" data-delay="200">
                            <td class="feature-name">Explanation Format</td>
                            <td class="traditional-value">Inconsistent</td>
                            <td class="gptsir-value">Step-wise, uniform</td>
                        </tr>
                        <tr class="table-row" data-delay="300">
                            <td class="feature-name">Tagging</td>
                            <td class="traditional-value"><span class="cross">❌</span> None</td>
                            <td class="gptsir-value"><span class="check">✅</span> Difficulty + Bloom level</td>
                        </tr>
                        <tr class="table-row" data-delay="400">
                            <td class="feature-name">Multilingual</td>
                            <td class="traditional-value"><span class="cross">❌</span></td>
                            <td class="gptsir-value"><span class="check">✅</span> Yes</td>
                        </tr>
                        <tr class="table-row" data-delay="500">
                            <td class="feature-name">Practice Mode</td>
                            <td class="traditional-value"><span class="cross">❌</span></td>
                            <td class="gptsir-value"><span class="check">✅</span> Play–Practice–Test</td>
                        </tr>
                        </tbody>
                    </table>

                    <div class="table-note">
                        <div class="note-icon">💡</div>
                        <p>These are not just more questions. They're better questions.<br>
                            And every one is <strong>AI-generated + educator-approved</strong>.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Learning Modes Section -->
    <section class="learning-modes-section section-bg-pattern-1">
        <div class="container">
            <div class="learning-modes-header">
                <h2 class="section-title">
                    Solve Doubts.<span class="highlight"> AI Tutor to Guide. </span><br>Practice that Sticks.
                </h2>
            </div>

            <div class="learning-modes-carousel">
                <div class="carousel-container">
                    <div class="carousel-track" id="learningModesTrack">
                        <!-- Instant Doubt Solving Mode Card -->
                        <div class="mode-card active" data-mode="play">
                            <div class="mode-card-content">
                                <div class="mode-icon">💡</div>
                                <h3 class="mode-title">Instant Doubt Solving</h3>
                                <p class="mode-description">Ask any question from the chapter and get accurate, curriculum-aligned answers instantly</p>
                            </div>
                            <div class="mode-image-wrapper">
                                <img src="${assetPath(src: 'gptsirlandingpage/doubts-portrait.png')}" alt="Instant Doubt Solving" class="mode-image">
                            </div>
                        </div>

                        <!-- AI Tutor Mode Card -->
                        <div class="mode-card" data-mode="practice">
                            <div class="mode-card-content">
                                <div class="mode-icon">📚</div>
                                <h3 class="mode-title">AI Tutor</h3>
                                <p class="mode-description">Learn smarter and faster with your 24/7 AI-powered personal tutor.</p>
                            </div>
                            <div class="mode-image-wrapper">
                                <img src="${assetPath(src: 'gptsirlandingpage/ai-tutor.png')}" alt="AI Tutor" class="mode-image">
                            </div>
                        </div>

                        <!-- Gamified Learning Mode Card -->
                        <div class="mode-card" data-mode="test">
                            <div class="mode-card-content">
                                <div class="mode-icon">🎮</div>
                                <h3 class="mode-title">Gamified Learning</h3>
                                <p class="mode-description">Game-like MCQ solving, deliberate practice with hints, and timed testing for exam readiness</p>
                            </div>
                            <div class="mode-image-wrapper">
                                <img src="${assetPath(src: 'gptsirlandingpage/playc-portrait.png')}" alt="Gamified Learning" class="mode-image">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Carousel Navigation Dots -->
                <div class="carousel-dots">
                    <button class="dot active" data-slide="0" aria-label="Play Mode"></button>
                    <button class="dot" data-slide="1" aria-label="Practice Mode"></button>
                    <button class="dot" data-slide="2" aria-label="Test Mode"></button>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section section-bg-pattern-2">
        <div class="container">
            <div class="testimonials-container">
                <div class="testimonials-header">
                    <h2 class="section-title">
                        Trusted by Students & <span class="highlight">Educators</span>
                    </h2>
                    <p class="section-subtitle">See what our users are saying about GPT Sir books</p>
                </div>

                <div class="testimonials-scroll-container">
                    <div class="testimonials-grid">
                        <div class="testimonial-card" data-delay="0">
                            <div class="testimonial-content">
                                <div class="user-avatar">
                                    <img src="${assetPath(src: 'gptsirlandingpage/student-2.jpg')}" alt="NEET Aspirant Avatar">
                                </div>
                                <p class="testimonial-text">I couldn't believe how many questions the book had—and they all made sense.</p>
                                <div class="testimonial-author">
                                    <span class="author-name">Class 12 NEET aspirant</span>
                                    <span class="author-location">Karnataka</span>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial-card" data-delay="100">
                            <div class="testimonial-content">
                                <div class="user-avatar">
                                    <img src="${assetPath(src: 'gptsirlandingpage/teacher-3.jpg')}" alt="Math Teacher Avatar">
                                </div>
                                <p class="testimonial-text">My students love the interactive format and instant feedback system.</p>
                                <div class="testimonial-author">
                                    <span class="author-name">Mathematics teacher</span>
                                    <span class="author-location">ICSE School, West Bengal</span>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial-card" data-delay="200">
                            <div class="testimonial-content">
                                <div class="user-avatar">
                                    <img src="${assetPath(src: 'gptsirlandingpage/teacher-1.jpg')}" alt="Parent Avatar">
                                </div>
                                <p class="testimonial-text">Finally, a book that actually understands the board exam pattern.</p>
                                <div class="testimonial-author">
                                    <span class="author-name">Parent of Class 10 CBSE student</span>
                                    <span class="author-location">Delhi</span>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial-card" data-delay="300">
                            <div class="testimonial-content">
                                <div class="user-avatar">
                                    <img src="${assetPath(src: 'gptsirlandingpage/student-1.jpg')}" alt="JEE Aspirant Avatar">
                                </div>
                                <p class="testimonial-text">The AI-generated questions are exactly what I needed for JEE preparation.</p>
                                <div class="testimonial-author">
                                    <span class="author-name">Class 12 JEE aspirant</span>
                                    <span class="author-location">Maharashtra</span>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial-card" data-delay="400">
                            <div class="testimonial-content">
                                <div class="user-avatar">
                                    <img src="${assetPath(src: 'gptsirlandingpage/teacher-2.jpg')}" alt="Science Teacher Avatar">
                                </div>
                                <p class="testimonial-text">The Play–Practice–Test modes changed the way my students revise.</p>
                                <div class="testimonial-author">
                                    <span class="author-name">Science teacher</span>
                                    <span class="author-location">CBSE School, Tamil Nadu</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories-section section-bg-pattern-1" id="categories">
        <div class="container">
            <div class="categories-container">
                <div class="categories-header">
                    <h2 class="section-title">
                        Explore Our <span class="highlight">Learning Categories</span>
                    </h2>
                    <p class="section-subtitle">Choose from our comprehensive collection of AI-powered books across different levels and subjects</p>
                </div>

                <div class="categories-content" id="categoriesContent">
                    <!-- Categories will be dynamically generated here -->
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section section-bg-pattern-2">
        <div class="container">
            <div class="faq-container">
                <div class="faq-header">
                    <h2 class="section-title">
                        Frequently Asked <span class="highlight">Questions</span>
                    </h2>
                    <p class="section-subtitle">Everything you need to know about GPT Sir books</p>
                </div>

                <div class="faq-accordion">
                    <div class="faq-item" data-delay="0">
                        <div class="faq-question">
                            <h3>How can I purchase a Book?</h3>
                            <div class="faq-icon">
                                <span class="plus">+</span>
                                <span class="minus">−</span>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Register or log in on the website, choose your book, click "Add to Cart", and complete the payment.</p>
                        </div>
                    </div>

                    <div class="faq-item" data-delay="100">
                        <div class="faq-question">
                            <h3>How do I interact with the AI Tutor?</h3>
                            <div class="faq-icon">
                                <span class="plus">+</span>
                                <span class="minus">−</span>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>You can type questions or upload images of problems. The AI will instantly respond with explanations or solutions in a chat.</p>
                        </div>
                    </div>

                    <div class="faq-item" data-delay="200">
                        <div class="faq-question">
                            <h3>Can I switch languages?</h3>
                            <div class="faq-icon">
                                <span class="plus">+</span>
                                <span class="minus">−</span>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Yes. We support English, Hindi, Kannada, and other regional languages.</p>
                        </div>
                    </div>
                    <div class="faq-item" data-delay="200">
                        <div class="faq-question">
                            <h3>Can I take tests to track my progress?</h3>
                            <div class="faq-icon">
                                <span class="plus">+</span>
                                <span class="minus">−</span>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Yes. GPT Sir Books offers practice and timed tests that simulate real exam conditions to help you assess and improve your performance.</p>
                        </div>
                    </div>

                    <div class="faq-item" data-delay="300">
                        <div class="faq-question">
                            <h3>What makes the AI-Powered Question Bank unique?</h3>
                            <div class="faq-icon">
                                <span class="plus">+</span>
                                <span class="minus">−</span>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>It offers 3× more solved and tagged questions than typical market guides—making it ideal for smart and thorough practice.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pre-footer CTA Section -->
    <section class="pre-footer-section">
        <div class="container">
            <div class="pre-footer-container">
                <div class="pre-footer-content">
                    <div class="pre-footer-icon">💡</div>
                    <div class="pre-footer-text">
                        <h2>Smarter Books Start Here.</h2>
                        <p>Explore our collection of AI-powered textbooks built for today's learners</p>
                    </div>
                    <a href="/sp/${session["siteName"]}/store" class="pre-footer-button" target="_blank">Get Started</a>
                </div>
            </div>
        </div>
    </section>
</main>

<!-- Footer Section -->
<footer class="footer" id="landingPage-footer">
    <div class="footer-container">
        <div class="footer-content">
            <!-- Brand Section -->
            <div class="footer-brand">
                <div class="footer-logo">
                    <img src="${assetPath(src: 'gptsirlandingpage/gptsirai-logo.png')}" alt="GPT Sir AI">
                </div>
                <p class="footer-description">
                    Tailored generative AI solutions driving innovation and efficiency across enterprises
                </p>
                <div class="footer-social">
                    <a href="https://www.youtube.com/channel/UC-87LRCX4mo9jmPCqG3S9ZQ" class="social-link" aria-label="YouTube">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                    </a>
                    <a href="https://www.instagram.com/gptsir_/" class="social-link" aria-label="Instagram">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </a>
                    <a href="https://www.facebook.com/profile.php?id=61561621995625" class="social-link" aria-label="Facebook">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="footer-section">
                <h3 class="footer-section-title">QUICK LINKS</h3>
                <ul class="footer-links">
                    <li><a href="https://www.wonderslate.com/funlearn/privacy">Privacy Policy</a></li>
                    <li><a href="https://gptsir.ai/gptsir/page/terms-and-conditions?pageId=67">Terms & Conditions</a></li>
                </ul>
            </div>

            <!-- Business Enquiry -->
            <div class="footer-section">
                <h3 class="footer-section-title">BUSINESS ENQUIRY</h3>
                <div class="footer-contact">
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <EMAIL>
                    </a>
                </div>
            </div>

            <!-- Product Support -->
            <div class="footer-section">
                <h3 class="footer-section-title">PRODUCT SUPPORT</h3>
                <div class="footer-contact">
                    <a href="tel:+916363371085" class="contact-link">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                        </svg>
                        +91 8088443860
                    </a>
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>
<script>
    var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
</script>
<asset:javascript src="landingpage/gptsirLandingpage.js"/>
